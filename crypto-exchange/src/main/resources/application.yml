# 服务器配置
server:
  port: 8080
  servlet:
    context-path: /api
    encoding:
      charset: UTF-8
      enabled: true
      force: true
  tomcat:
    uri-encoding: UTF-8
    max-threads: 200
    min-spare-threads: 10

# Spring配置
spring:
  application:
    name: crypto-exchange-backend
  
  # 环境配置
  profiles:
    active: dev
  
  # 数据源配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ********************************************************************************************************************************************************
    username: root
    password: 
    druid:
      # 初始连接数
      initial-size: 5
      # 最小连接池数量
      min-idle: 10
      # 最大连接池数量
      max-active: 20
      # 配置获取连接等待超时的时间
      max-wait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      time-between-eviction-runs-millis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      min-evictable-idle-time-millis: 300000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      max-evictable-idle-time-millis: 900000
      # 配置检测连接是否有效
      validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      # 打开PSCache，并且指定每个连接上PSCache的大小
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
      filters: stat,wall,slf4j
      # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
      connection-properties: druid.stat.mergeSql\=true;druid.stat.slowSqlMillis\=5000
      # 配置DruidStatFilter
      web-stat-filter:
        enabled: true
        url-pattern: "/*"
        exclusions: "*.js,*.gif,*.jpg,*.bmp,*.png,*.css,*.ico,/druid/*"
      # 配置DruidStatViewServlet
      stat-view-servlet:
        enabled: true
        url-pattern: "/druid/*"
        # IP白名单(没有配置或者为空，则允许所有访问)
        allow: 127.0.0.1,*************
        # IP黑名单 (存在共同时，deny优先于allow)
        deny: ************
        # 禁用HTML页面上的"Reset All"功能
        reset-enable: false
        # 登录名
        login-username: admin
        # 登录密码
        login-password: 123456
  
  # Redis配置
  data:
    redis:
      host: localhost
      port: 6379
      password: 
      database: 0
      timeout: 10000ms
      lettuce:
        pool:
          # 连接池最大连接数
          max-active: 200
          # 连接池最大阻塞等待时间（使用负值表示没有限制）
          max-wait: -1ms
          # 连接池中的最大空闲连接
          max-idle: 10
          # 连接池中的最小空闲连接
          min-idle: 0
  
  # 邮件配置
  mail:
    host: smtp.qq.com
    port: 587
    username: <EMAIL>
    password: your-email-password
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true
  
  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
  
  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false

# MyBatis Plus配置
mybatis-plus:
  configuration:
    # 是否开启自动驼峰命名规则映射
    map-underscore-to-camel-case: true
    # 开启Mybatis二级缓存，默认为true
    cache-enabled: false
    # 配置JdbcTypeForNull
    jdbc-type-for-null: 'null'
    # 打印SQL语句
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      # 全局默认主键类型
      id-type: ASSIGN_ID
      # 逻辑删除配置
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
  # 搜索指定包别名
  type-aliases-package: com.cryptoexchange.entity
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapper-locations: classpath*:mapper/**/*Mapper.xml

# Knife4j配置
knife4j:
  enable: true
  openapi:
    title: 虚拟币交易所API文档
    description: 虚拟币交易所后端接口文档
    email: <EMAIL>
    concat: 管理员
    url: https://www.cryptoexchange.com
    version: v1.0.0
    license: Apache 2.0
    license-url: https://www.apache.org/licenses/LICENSE-2.0
    terms-of-service-url: https://www.cryptoexchange.com/terms
    group:
      default:
        group-name: default
        api-rule: package
        api-rule-resources:
          - com.cryptoexchange.controller

# 日志配置
logging:
  level:
    com.cryptoexchange: debug
    org.springframework.security: debug
    org.springframework.web: info
    org.mybatis: debug
  pattern:
    console: '%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n'
    file: '%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n'
  file:
    name: logs/crypto-exchange.log
    max-size: 10MB
    max-history: 30

# 应用自定义配置
app:
  # JWT配置
  jwt:
    # 密钥
    secret: crypto-exchange-jwt-secret-key-2024
    # 过期时间（秒）
    expiration: 86400
    # 刷新令牌过期时间（秒）
    refresh-expiration: 604800
  
  # 安全配置
  security:
    # 允许跨域的域名
    allowed-origins:
      - http://localhost:3000
      - http://localhost:8080
      - http://127.0.0.1:3000
      - http://127.0.0.1:8080
    # 不需要认证的路径
    permit-all-paths:
      - /auth/**
      - /public/**
      - /doc.html
      - /swagger-ui/**
      - /v3/api-docs/**
      - /webjars/**
      - /favicon.ico
      - /druid/**
      - /actuator/**
      - /ws/**
  
  # 文件存储配置
  file:
    # 上传路径
    upload-path: ./uploads/
    # 访问路径
    access-path: /files/**
    # MinIO配置
    minio:
      endpoint: http://localhost:9000
      access-key: minioadmin
      secret-key: minioadmin
      bucket-name: crypto-exchange
  
  # 交易配置
  trading:
    # 手续费率（千分之几）
    fee-rate: 1
    # 最小交易金额
    min-trade-amount: 10
    # 最大交易金额
    max-trade-amount: 1000000
    # 价格精度
    price-precision: 8
    # 数量精度
    quantity-precision: 8
  
  # WebSocket配置
  websocket:
    # 允许的源
    allowed-origins:
      - http://localhost:3000
      - http://127.0.0.1:3000
    # 心跳间隔（秒）
    heartbeat-interval: 30
    # 连接超时（秒）
    connection-timeout: 60

---
# 开发环境配置
spring:
  config:
    activate:
      on-profile: dev
  datasource:
    url: *************************************************************************************************************************************************************
  data:
    redis:
      database: 0

logging:
  level:
    root: info
    com.cryptoexchange: debug

---
# 测试环境配置
spring:
  config:
    activate:
      on-profile: test
  datasource:
    url: **************************************************************************************************************************************************************
  data:
    redis:
      database: 1

logging:
  level:
    root: warn
    com.cryptoexchange: info

---
# 生产环境配置
spring:
  config:
    activate:
      on-profile: prod
  datasource:
    url: ********************************************************************************************************************************************************
  data:
    redis:
      database: 0

logging:
  level:
    root: warn
    com.cryptoexchange: info
  file:
    name: /var/log/crypto-exchange/crypto-exchange.log