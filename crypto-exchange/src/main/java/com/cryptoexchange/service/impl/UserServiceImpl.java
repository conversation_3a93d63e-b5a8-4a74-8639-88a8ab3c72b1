package com.cryptoexchange.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cryptoexchange.common.PageResult;
import com.cryptoexchange.common.Result;
import com.cryptoexchange.dto.request.AdminUserQueryRequest;
import com.cryptoexchange.dto.request.KycReviewRequest;
import com.cryptoexchange.dto.request.UserNotificationQueryRequest;
import com.cryptoexchange.dto.response.AdminUserResponse;
import com.cryptoexchange.dto.response.CheckInRecordResponse;
import com.cryptoexchange.dto.response.CheckInResponse;
import com.cryptoexchange.dto.response.UnreadNotificationCountResponse;
import com.cryptoexchange.dto.response.UserNotificationResponse;
import com.cryptoexchange.dto.response.UserStatisticsResponse;
import com.cryptoexchange.dto.response.UserTradingOverviewResponse;
import com.cryptoexchange.dto.response.UserAssetOverviewResponse;
import com.cryptoexchange.dto.response.UserPermissionsResponse;
import com.cryptoexchange.dto.response.ReferralUserResponse;
import com.cryptoexchange.dto.response.ReferralInfoResponse;
import com.cryptoexchange.dto.response.UserLevelResponse;
import com.cryptoexchange.dto.response.LoginHistoryResponse;
import com.cryptoexchange.dto.response.AvatarUploadResponse;
import com.cryptoexchange.dto.response.KycStatusResponse;
import com.cryptoexchange.dto.response.SecuritySettingsResponse;
import com.cryptoexchange.dto.response.UserProfileResponse;
import com.cryptoexchange.dto.request.SetTradingPasswordRequest;
import com.cryptoexchange.dto.request.ChangePasswordRequest;
import com.cryptoexchange.dto.request.UpdateUserProfileRequest;
import com.cryptoexchange.dto.request.UpdateSecuritySettingsRequest;
import com.cryptoexchange.dto.request.KycApplicationRequest;
import com.cryptoexchange.dto.request.RealNameAuthRequest;
import com.cryptoexchange.dto.request.OperationLogQueryRequest;
import org.springframework.web.multipart.MultipartFile;
import com.cryptoexchange.entity.User;
import com.cryptoexchange.exception.BusinessException;
import com.cryptoexchange.mapper.UserMapper;
import com.cryptoexchange.service.UserService;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;

/**
 * 用户服务实现类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@RequiredArgsConstructor
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {

    private static final Logger log = LoggerFactory.getLogger(UserServiceImpl.class);

    private final UserMapper userMapper;
    private final PasswordEncoder passwordEncoder;

    @Override
    public User findByUsername(String username) {
        return userMapper.findByUsername(username);
    }

    @Override
    public User findByEmail(String email) {
        return userMapper.findByEmail(email);
    }

    @Override
    public User findByPhone(String phone) {
        return userMapper.findByPhone(phone);
    }

    @Override
    public User findByReferralCode(String referralCode) {
        return userMapper.findByReferralCode(referralCode);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public User createUser(User user) {
        // 检查用户名是否已存在
        if (existsByUsername(user.getUsername())) {
            throw new BusinessException("用户名已存在");
        }
        
        // 检查邮箱是否已存在
        if (existsByEmail(user.getEmail())) {
            throw new BusinessException("邮箱已存在");
        }
        
        // 检查手机号是否已存在（如果提供）
        if (StringUtils.hasText(user.getPhone()) && existsByPhone(user.getPhone())) {
            throw new BusinessException("手机号已存在");
        }
        
        // 生成唯一推荐码
        if (!StringUtils.hasText(user.getReferralCode())) {
            user.setReferralCode(generateUniqueReferralCode());
        }
        
        // 设置默认值
        user.setStatus(1); // 正常状态
        user.setUserType(0); // 普通用户
        user.setKycStatus(0); // 未认证
        user.setTwoFactorEnabled(false);
        user.setFeeLevel(0);
        user.setTotalTradeVolume(BigDecimal.ZERO);
        user.setDeleted(0);
        user.setCreateTime(LocalDateTime.now());
        user.setUpdateTime(LocalDateTime.now());
        
        // 保存用户
        save(user);
        
        log.info("用户创建成功: {}", user.getUsername());
        return user;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public User updateUser(User user) {
        user.setUpdateTime(LocalDateTime.now());
        updateById(user);
        log.info("用户信息更新成功: {}", user.getId());
        return user;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateUserStatus(Long userId, Integer status) {
        int result = userMapper.updateUserStatus(userId, status, LocalDateTime.now());
        if (result == 0) {
            throw new BusinessException("用户不存在或状态更新失败");
        }
        log.info("用户状态更新成功: userId={}, status={}", userId, status);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePassword(Long userId, String newPassword) {
        String encodedPassword = passwordEncoder.encode(newPassword);
        int result = userMapper.updatePassword(userId, encodedPassword, LocalDateTime.now());
        if (result == 0) {
            throw new BusinessException("用户不存在或密码更新失败");
        }
        log.info("用户密码更新成功: userId={}", userId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTradingPassword(Long userId, String tradingPassword) {
        String encodedPassword = passwordEncoder.encode(tradingPassword);
        int result = userMapper.updateTradingPassword(userId, encodedPassword, LocalDateTime.now());
        if (result == 0) {
            throw new BusinessException("用户不存在或交易密码更新失败");
        }
        log.info("用户交易密码更新成功: userId={}", userId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AvatarUploadResponse uploadAvatar(Long userId, MultipartFile file) {
        User user = getById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        // 这里需要实现文件上传逻辑，例如上传到OSS或本地存储
        // 假设文件上传成功后返回可访问的URL
        String avatarUrl = "/path/to/uploaded/avatar.jpg"; // 替换为实际的上传逻辑

        user.setAvatar(avatarUrl);
        user.setUpdateTime(LocalDateTime.now());
        updateById(user);
        log.info("用户头像更新成功: userId={}, avatarUrl={}", userId, avatarUrl);

        return new AvatarUploadResponse(avatarUrl, "avatar.jpg", 0L, "image/jpeg");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateNickname(Long userId, String nickname) {
        User user = getById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        user.setNickname(nickname);
        user.setUpdateTime(LocalDateTime.now());
        updateById(user);
        log.info("用户昵称更新成功: userId={}", userId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateLanguage(Long userId, String language) {
        User user = getById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        user.setLanguage(language);
        user.setUpdateTime(LocalDateTime.now());
        updateById(user);
        log.info("用户语言偏好更新成功: userId={}, language={}", userId, language);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTimezone(Long userId, String timezone) {
        User user = getById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        user.setTimezone(timezone);
        user.setUpdateTime(LocalDateTime.now());
        updateById(user);
        log.info("用户时区更新成功: userId={}, timezone={}", userId, timezone);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTwoFactorAuth(Long userId, Boolean enabled) {
        User user = getById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        user.setTwoFactorEnabled(enabled);
        user.setUpdateTime(LocalDateTime.now());
        updateById(user);
        log.info("用户两步验证更新成功: userId={}, enabled={}", userId, enabled);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateKycStatus(Long userId, Integer kycStatus) {
        int result = userMapper.updateKycStatus(userId, kycStatus, LocalDateTime.now());
        if (result == 0) {
            throw new BusinessException("用户不存在或KYC状态更新失败");
        }
        log.info("用户KYC状态更新成功: userId={}, kycStatus={}", userId, kycStatus);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateRealNameInfo(Long userId, String realName, String idNumber) {
        User user = getById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        user.setRealName(realName);
        user.setIdCard(idNumber);
        user.setUpdateTime(LocalDateTime.now());
        updateById(user);
        log.info("用户实名信息更新成功: userId={}", userId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateFeeLevel(Long userId, Integer feeLevel) {
        User user = getById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        user.setFeeLevel(feeLevel);
        user.setUpdateTime(LocalDateTime.now());
        updateById(user);
        log.info("用户手续费等级更新成功: userId={}, feeLevel={}", userId, feeLevel);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTradingVolume(Long userId, BigDecimal volume) {
        int result = userMapper.updateTradingVolume(userId, volume, LocalDateTime.now());
        if (result == 0) {
            throw new BusinessException("用户不存在或交易量更新失败");
        }
        log.info("用户交易量更新成功: userId={}, volume={}", userId, volume);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateLastLoginInfo(Long userId, String loginIp, String userAgent) {
        int result = userMapper.updateLastLoginInfo(userId, loginIp, LocalDateTime.now(), userAgent);
        if (result == 0) {
            throw new BusinessException("用户不存在或登录信息更新失败");
        }
        log.info("用户登录信息更新成功: userId={}, loginIp={}", userId, loginIp);
    }

    @Override
    public boolean existsByUsername(String username) {
        return userMapper.countByUsername(username) > 0;
    }

    @Override
    public boolean existsByEmail(String email) {
        return userMapper.countByEmail(email) > 0;
    }

    @Override
    public boolean existsByPhone(String phone) {
        return userMapper.countByPhone(phone) > 0;
    }

    @Override
    public boolean existsByReferralCode(String referralCode) {
        return userMapper.countByReferralCode(referralCode) > 0;
    }

    @Override
    public String generateUniqueReferralCode() {
        String characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        Random random = new Random();
        String referralCode;
        
        do {
            StringBuilder sb = new StringBuilder(6);
            for (int i = 0; i < 6; i++) {
                sb.append(characters.charAt(random.nextInt(characters.length())));
            }
            referralCode = sb.toString();
        } while (existsByReferralCode(referralCode));
        
        return referralCode;
    }

    @Override
    public PageResult<User> findUsers(Integer page, Integer size, String keyword, 
                                     Integer status, Integer userType, Integer kycStatus) {
        Page<User> pageObj = new Page<>(page, size);
        IPage<User> result = userMapper.findUsers(pageObj, keyword, status, userType, kycStatus);
        return PageResult.of(result);
    }

    @Override
    public PageResult<User> findReferralUsers(Long userId, Integer page, Integer size) {
        Page<User> pageObj = new Page<>(page, size);
        IPage<User> result = userMapper.findReferralUsers(pageObj, userId);
        return PageResult.of(result);
    }

    @Override
    public Map<String, Object> getUserStatistics(Long userId) {
        Map<String, Object> statistics = userMapper.getUserStatistics(userId);
        if (statistics == null) {
            statistics = new HashMap<>();
        }
        return statistics;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void softDeleteUser(Long userId) {
        int result = userMapper.softDeleteUser(userId, LocalDateTime.now());
        if (result == 0) {
            throw new BusinessException("用户不存在或删除失败");
        }
        log.info("用户软删除成功: userId={}", userId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void restoreUser(Long userId) {
        int result = userMapper.restoreUser(userId, LocalDateTime.now());
        if (result == 0) {
            throw new BusinessException("用户不存在或恢复失败");
        }
        log.info("用户恢复成功: userId={}", userId);
    }

    @Override
    public boolean verifyPassword(Long userId, String rawPassword) {
        User user = getById(userId);
        if (user == null) {
            return false;
        }
        return passwordEncoder.matches(rawPassword, user.getPassword());
    }

    @Override
    public boolean verifyTradingPassword(Long userId, String rawTradingPassword) {
        User user = getById(userId);
        if (user == null || user.getTradePassword() == null) {
            return false;
        }
        return passwordEncoder.matches(rawTradingPassword, user.getTradePassword());
    }

    @Override
    public Result<UserStatisticsResponse> getUserStatistics(String period) {
        // 实现用户统计功能
        UserStatisticsResponse response = new UserStatisticsResponse();
        // 这里可以根据period参数获取不同时间段的统计数据
        // 暂时返回空的响应对象
        return Result.success(response);
    }

    @Override
    public Result<Void> reviewKyc(Long kycId, KycReviewRequest request) {
        // 实现KYC审核功能
        // 这里可以根据request参数进行KYC审核
        // 暂时返回成功响应
        return Result.success();
    }

    @Override
    public Result<Void> freezeUser(Long userId, String reason) {
        // 实现用户冻结功能
        // 这里可以根据userId和reason参数冻结用户
        // 暂时返回成功响应
        return Result.success();
    }

    @Override
    public Result<Void> unfreezeUser(Long userId) {
        // 实现用户解冻功能
        // 这里可以根据userId参数解冻用户
        // 暂时返回成功响应
        return Result.success();
    }

    @Override
    public PageResult<AdminUserResponse> getUsers(AdminUserQueryRequest request) {
        // 实现管理员用户查询功能
        // 这里可以根据request参数查询用户列表
        // 暂时返回空的分页结果
        return PageResult.empty(1L, 10L);
    }

    @Override
    public Result<Void> batchMarkNotificationsAsRead(Long userId, List<Long> notificationIds) {
        // 实现批量标记通知为已读功能
        // 这里可以根据userId和notificationIds参数批量标记通知
        // 暂时返回成功响应
        return Result.success();
    }

    @Override
    public PageResult<UserNotificationResponse> getUserNotifications(UserNotificationQueryRequest request) {
        // 实现用户通知查询功能
        // 这里可以根据request参数查询用户通知列表
        // 暂时返回空的分页结果
        return PageResult.empty(1L, 10L);
    }

    @Override
    public Result<UnreadNotificationCountResponse> getUnreadNotificationCount(Long userId) {
        // 实现获取未读通知数量功能
        // 这里可以根据userId参数查询用户未读通知数量
        // 暂时返回0个未读通知
        UnreadNotificationCountResponse response = new UnreadNotificationCountResponse();
        response.setTotalUnreadCount(0);
        return Result.success(response);
    }

    @Override
    public Result<Void> markNotificationAsRead(Long userId, Long notificationId) {
        // 实现标记通知为已读功能
        // 这里可以根据userId和notificationId参数标记指定通知为已读
        // 暂时返回成功响应
        return Result.success();
    }

    @Override
    public Result<CheckInRecordResponse> getCheckInRecords(Long userId, Integer pageNum, Integer pageSize) {
        // 实现获取签到记录功能
        // 这里可以根据userId、pageNum和pageSize参数查询用户签到记录
        // 暂时返回空的签到记录响应
        CheckInRecordResponse response = new CheckInRecordResponse();
        return Result.success(response);
    }

    @Override
    public Result<CheckInResponse> checkIn(Long userId) {
        // 实现用户签到功能
        // 这里可以根据userId参数执行用户签到操作
        // 暂时返回空的签到响应
        CheckInResponse response = new CheckInResponse();
        return Result.success(response);
    }

    @Override
    public Result<UserTradingOverviewResponse> getUserTradingOverview(Long userId) {
        // 实现获取用户交易概览功能
        // 这里可以根据userId参数查询用户交易概览信息
        // 暂时返回空的交易概览响应
        UserTradingOverviewResponse response = new UserTradingOverviewResponse();
        return Result.success(response);
    }

    @Override
    public Result<UserAssetOverviewResponse> getUserAssetOverview(Long userId) {
        // 实现获取用户资产概览功能
        // 这里可以根据userId参数查询用户资产概览信息
        // 暂时返回空的资产概览响应
        UserAssetOverviewResponse response = new UserAssetOverviewResponse();
        return Result.success(response);
    }

    @Override
    public Result<UserPermissionsResponse> getUserPermissions(Long userId) {
        // 实现获取用户权限功能
        // 这里可以根据userId参数查询用户权限信息
        // 暂时返回空的权限响应
        UserPermissionsResponse response = new UserPermissionsResponse();
        return Result.success(response);
    }

    @Override
    public Result<ReferralUserResponse> getReferralUsers(Long userId, Integer pageNum, Integer pageSize) {
        // 实现获取推荐用户功能
        // 这里可以根据userId、pageNum和pageSize参数查询推荐用户列表
        // 暂时返回空的推荐用户响应
        ReferralUserResponse response = new ReferralUserResponse();
        return Result.success(response);
    }

    @Override
    public Result<ReferralInfoResponse> getReferralInfo(Long userId) {
        // 实现获取推荐信息功能
        // 这里可以根据userId参数查询用户推荐信息
        // 暂时返回空的推荐信息响应
        ReferralInfoResponse response = new ReferralInfoResponse();
        return Result.success(response);
    }

    @Override
    public Result<UserLevelResponse> getUserLevel(Long userId) {
        // 实现获取用户等级功能
        // 这里可以根据userId参数查询用户等级信息
        // 暂时返回空的用户等级响应
        UserLevelResponse response = new UserLevelResponse();
        return Result.success(response);
    }

    @Override
    public Result<LoginHistoryResponse> getLoginHistory(Long userId, Integer pageNum, Integer pageSize) {
        // 实现获取登录历史功能
        // 这里可以根据userId、pageNum和pageSize参数查询用户登录历史
        // 暂时返回空的登录历史响应
        LoginHistoryResponse response = new LoginHistoryResponse();
        return Result.success(response);
    }



    @Override
    public Result<KycStatusResponse> getKycStatus(Long userId) {
        // 实现获取KYC状态功能
        // 这里可以根据userId参数查询用户KYC状态
        // 暂时返回空的KYC状态响应
        KycStatusResponse response = new KycStatusResponse();
        return Result.success(response);
    }

    @Override
    public Result<SecuritySettingsResponse> getSecuritySettings(Long userId) {
        // TODO: 实现获取安全设置的逻辑
        return Result.success(new SecuritySettingsResponse());
    }

    @Override
    public Result<Void> setTradingPassword(Long userId, SetTradingPasswordRequest request) {
        // TODO: 实现设置交易密码的逻辑
        return Result.success(null);
    }

    @Override
    public Result<Void> changePassword(Long userId, ChangePasswordRequest request) {
        // TODO: 实现修改密码的逻辑
        return Result.success(null);
    }

    @Override
    public Result<Void> updateUserProfile(Long userId, UpdateUserProfileRequest request) {
        // TODO: 实现更新用户资料的逻辑
        return Result.success(null);
    }

    @Override
    public Result<UserProfileResponse> getUserProfile(Long userId) {
        // TODO: 实现获取用户资料的逻辑
        return Result.success(new UserProfileResponse());
    }

    @Override
    public Result<Void> updateSecuritySettings(Long userId, UpdateSecuritySettingsRequest request) {
        // TODO: 实现更新安全设置的逻辑
        return Result.success(null);
    }

    @Override
    public Result<Void> applyKyc(Long userId, KycApplicationRequest request) {
        // TODO: 实现申请KYC认证的逻辑
        return Result.success(null);
    }

    @Override
    public Result<Void> realNameAuth(Long userId, RealNameAuthRequest request) {
        // TODO: 实现实名认证的逻辑
        return Result.success(null);
    }

    @Override
    public PageResult<Object> getOperationLogs(OperationLogQueryRequest request) {
        // TODO: 实现获取操作日志的逻辑
        return PageResult.empty((long)request.getPageNum(), (long)request.getPageSize());
    }

    @Override
    public boolean verifyTwoFactorAuth(Long userId, String code) {
        // TODO: 实现双因子认证验证的逻辑
        return true;
    }
}