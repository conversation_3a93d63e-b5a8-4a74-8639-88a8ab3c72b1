package com.cryptoexchange.service;

import com.cryptoexchange.dto.request.DepositRequest;
import com.cryptoexchange.dto.request.WithdrawRequest;
import com.cryptoexchange.dto.request.TransferRequest;
import com.cryptoexchange.dto.response.WalletResponse;
import com.cryptoexchange.dto.response.TransactionResponse;
import com.cryptoexchange.entity.UserWallet;
import com.cryptoexchange.common.PageResult;
import com.cryptoexchange.common.Result;

import java.math.BigDecimal;
import java.util.List;

/**
 * 钱包服务接口
 * 负责用户资产管理、充值提现、资产转账等功能
 */
public interface WalletService {
    
    /**
     * 获取用户钱包列表
     * @param userId 用户ID
     * @param walletType 钱包类型（可选）
     * @return 钱包列表
     */
    Result<List<WalletResponse>> getUserWallets(Long userId, Integer walletType);
    
    /**
     * 获取用户钱包列表（支持币种过滤）
     * @param userId 用户ID
     * @param currency 币种（可选）
     * @param walletType 钱包类型（可选）
     * @return 钱包列表
     */
    List<WalletResponse> getUserWallets(Long userId, String currency, Integer walletType);
    
    /**
     * 获取用户指定币种钱包
     * @param userId 用户ID
     * @param currency 币种
     * @param walletType 钱包类型
     * @return 钱包信息
     */
    Result<WalletResponse> getUserWallet(Long userId, String currency, Integer walletType);
    
    /**
     * 创建用户钱包
     * @param userId 用户ID
     * @param currency 币种
     * @param walletType 钱包类型
     * @return 创建结果
     */
    Result<WalletResponse> createUserWallet(Long userId, String currency, Integer walletType);
    
    /**
     * 更新用户余额
     * @param userId 用户ID
     * @param currency 币种
     * @param amount 金额变化（正数增加，负数减少）
     * @param type 操作类型
     * @return 操作结果
     */
    Result<Void> updateBalance(Long userId, String currency, BigDecimal amount, Integer type);
    
    /**
     * 冻结用户余额
     * @param userId 用户ID
     * @param currency 币种
     * @param amount 冻结金额
     * @return 操作结果
     */
    Result<Void> freezeBalance(Long userId, String currency, BigDecimal amount);
    
    /**
     * 解冻用户余额
     * @param userId 用户ID
     * @param currency 币种
     * @param amount 解冻金额
     * @return 操作结果
     */
    Result<Void> unfreezeBalance(Long userId, String currency, BigDecimal amount);
    
    /**
     * 用户充值
     * @param request 充值请求
     * @return 充值结果
     */
    Result<TransactionResponse> deposit(DepositRequest request);
    
    /**
     * 用户提现
     * @param request 提现请求
     * @return 提现结果
     */
    Result<TransactionResponse> withdraw(WithdrawRequest request);
    
    /**
     * 资产转账（钱包间转账）
     * @param request 转账请求
     * @return 转账结果
     */
    Result<Void> transfer(TransferRequest request);
    
    /**
     * 获取用户交易记录
     * @param userId 用户ID
     * @param currency 币种（可选）
     * @param type 交易类型（可选）
     * @param pageNum 页码
     * @param pageSize 页大小
     * @return 交易记录列表
     */
    Result<PageResult<TransactionResponse>> getUserTransactions(Long userId, String currency, 
                                                               Integer type, Integer pageNum, Integer pageSize);
    
    /**
     * 获取用户总资产（按USD计算）
     * @param userId 用户ID
     * @return 总资产
     */
    Result<BigDecimal> getUserTotalAssets(Long userId);
    
    /**
     * 获取用户资产分布
     * @param userId 用户ID
     * @return 资产分布
     */
    Result<Object> getUserAssetDistribution(Long userId);
    
    /**
     * 生成充值地址
     * @param userId 用户ID
     * @param currency 币种
     * @return 充值地址
     */
    Result<String> generateDepositAddress(Long userId, String currency);
    
    /**
     * 验证提现地址
     * @param currency 币种
     * @param address 地址
     * @return 验证结果
     */
    Result<Boolean> validateWithdrawAddress(String currency, String address);
    
    /**
     * 检查用户余额是否足够
     * @param userId 用户ID
     * @param currency 币种
     * @param amount 所需金额
     * @param walletType 钱包类型
     * @return 检查结果
     */
    boolean checkBalance(Long userId, String currency, BigDecimal amount, Integer walletType);
    
    /**
     * 获取币种汇率
     * @param fromCurrency 源币种
     * @param toCurrency 目标币种
     * @return 汇率
     */
    Result<BigDecimal> getExchangeRate(String fromCurrency, String toCurrency);
    
    /**
     * 资产快照（用于风控和统计）
     * @param userId 用户ID
     * @return 资产快照
     */
    Result<Object> createAssetSnapshot(Long userId);
    
    /**
     * 批量更新用户余额（用于系统调账）
     * @param adjustments 调账列表
     * @return 操作结果
     */
    Result<Void> batchUpdateBalance(List<Object> adjustments);
    
    /**
     * 获取钱包统计信息
     * @param currency 币种（可选）
     * @return 统计信息
     */
    Result<Object> getWalletStatistics(String currency);
}