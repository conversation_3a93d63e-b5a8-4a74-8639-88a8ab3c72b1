package com.cryptoexchange.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.Min;
import java.math.BigDecimal;
import java.util.Map;

/**
 * 解冻余额请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "解冻余额请求")
public class UnfreezeBalanceRequest {

    @NotNull(message = "用户ID不能为空")
    @Min(value = 1, message = "用户ID必须大于0")
    @Schema(description = "用户ID", example = "123456", required = true)
    private Long userId;

    @NotBlank(message = "币种不能为空")
    @Schema(description = "币种符号", example = "BTC", required = true)
    private String symbol;

    @Schema(description = "解冻金额（不填则解冻全部）", example = "1.5")
    @DecimalMin(value = "0.00000001", message = "解冻金额必须大于0")
    private BigDecimal amount;

    @Schema(description = "冻结记录ID")
    private Long freezeRecordId;

    @NotBlank(message = "解冻类型不能为空")
    @Schema(description = "解冻类型", example = "TRADING", allowableValues = {"TRADING", "WITHDRAWAL", "DEPOSIT", "TRANSFER", "ALL"}, required = true)
    private String unfreezeType;

    @NotBlank(message = "解冻原因不能为空")
    @Schema(description = "解冻原因", example = "风险解除", required = true)
    private String reason;

    @Schema(description = "解冻原因代码", example = "RISK_CLEARED")
    private String reasonCode;

    @Schema(description = "解冻方式", example = "MANUAL", allowableValues = {"MANUAL", "AUTO", "SCHEDULED", "CONDITIONAL"})
    private String unfreezeMethod = "MANUAL";

    @Schema(description = "解冻优先级", example = "MEDIUM", allowableValues = {"LOW", "MEDIUM", "HIGH", "URGENT"})
    private String priority = "MEDIUM";

    @Schema(description = "解冻范围", example = "ACCOUNT", allowableValues = {"ACCOUNT", "SYMBOL", "GLOBAL"})
    private String scope = "ACCOUNT";

    @Schema(description = "关联订单ID")
    private Long orderId;

    @Schema(description = "关联交易ID")
    private String tradeId;

    @Schema(description = "操作人员ID")
    private Long operatorId;

    @Schema(description = "操作人员姓名")
    private String operatorName;

    @Schema(description = "操作部门")
    private String department;

    @Schema(description = "审批状态", example = "PENDING", allowableValues = {"PENDING", "APPROVED", "REJECTED"})
    private String approvalStatus = "PENDING";

    @Schema(description = "审批人员ID")
    private Long approverId;

    @Schema(description = "审批意见")
    private String approvalComment;

    @Schema(description = "是否发送通知", example = "true")
    private Boolean sendNotification = true;

    @Schema(description = "通知方式", allowableValues = {"EMAIL", "SMS", "PUSH", "ALL"})
    private String[] notificationMethods;

    @Schema(description = "证据文件URL列表")
    private String[] evidenceUrls;

    @Schema(description = "详细说明")
    private String description;

    @Schema(description = "内部备注")
    private String internalNotes;

    @Schema(description = "客户可见备注")
    private String customerNotes;

    @Schema(description = "解冻标签")
    private String[] tags;

    @Schema(description = "自定义属性")
    private Map<String, Object> customAttributes;

    @Schema(description = "合规验证")
    private Map<String, String> complianceVerification;

    @Schema(description = "解冻条件验证")
    private Map<String, Object> conditionVerification;

    @Schema(description = "是否部分解冻", example = "false")
    private Boolean isPartialUnfreeze = false;

    @Schema(description = "解冻百分比（0-100）", example = "100")
    private Integer unfreezePercentage = 100;

    @Schema(description = "是否强制解冻", example = "false")
    private Boolean forceUnfreeze = false;

    @Schema(description = "强制解冻授权码")
    private String forceUnfreezeAuthCode;

    @Schema(description = "风险评估结果")
    private String riskAssessment;

    @Schema(description = "KYC验证状态")
    private String kycVerificationStatus;

    @Schema(description = "AML检查结果")
    private String amlCheckResult;

    @Schema(description = "备注信息")
    private String remarks;

    public UnfreezeBalanceRequest() {}

    public UnfreezeBalanceRequest(Long userId, String symbol, String unfreezeType, String reason) {
        this.userId = userId;
        this.symbol = symbol;
        this.unfreezeType = unfreezeType;
        this.reason = reason;
        this.unfreezeMethod = "MANUAL";
        this.priority = "MEDIUM";
        this.scope = "ACCOUNT";
        this.approvalStatus = "PENDING";
        this.sendNotification = true;
        this.isPartialUnfreeze = false;
        this.unfreezePercentage = 100;
        this.forceUnfreeze = false;
    }

    public UnfreezeBalanceRequest(Long userId, String symbol, BigDecimal amount, String unfreezeType, String reason) {
        this.userId = userId;
        this.symbol = symbol;
        this.amount = amount;
        this.unfreezeType = unfreezeType;
        this.reason = reason;
        this.unfreezeMethod = "MANUAL";
        this.priority = "MEDIUM";
        this.scope = "ACCOUNT";
        this.approvalStatus = "PENDING";
        this.sendNotification = true;
        this.isPartialUnfreeze = amount != null;
        this.unfreezePercentage = 100;
        this.forceUnfreeze = false;
    }

    public UnfreezeBalanceRequest(Long freezeRecordId, String reason, Long operatorId) {
        this.freezeRecordId = freezeRecordId;
        this.reason = reason;
        this.operatorId = operatorId;
        this.unfreezeMethod = "MANUAL";
        this.priority = "MEDIUM";
        this.scope = "ACCOUNT";
        this.approvalStatus = "PENDING";
        this.sendNotification = true;
        this.isPartialUnfreeze = false;
        this.unfreezePercentage = 100;
        this.forceUnfreeze = false;
    }

    /**
     * 验证解冻配置
     */
    public boolean isValidUnfreezeConfig() {
        if (freezeRecordId == null && (userId == null || symbol == null)) {
            return false;
        }
        
        if (isPartialUnfreeze && amount == null && unfreezePercentage == 100) {
            return false;
        }
        
        if (unfreezePercentage != null && (unfreezePercentage < 1 || unfreezePercentage > 100)) {
            return false;
        }
        
        return true;
    }

    /**
     * 是否需要审批
     */
    public boolean needApproval() {
        return "HIGH".equals(priority) || "URGENT".equals(priority) || 
               forceUnfreeze || "GLOBAL".equals(scope) ||
               (amount != null && amount.compareTo(new BigDecimal("10000")) > 0);
    }

    /**
     * 是否为高风险操作
     */
    public boolean isHighRiskOperation() {
        return forceUnfreeze || "ALL".equals(unfreezeType) || "GLOBAL".equals(scope) ||
               "URGENT".equals(priority);
    }

    /**
     * 是否需要额外验证
     */
    public boolean needAdditionalVerification() {
        return forceUnfreeze || isHighRiskOperation() || 
               (amount != null && amount.compareTo(new BigDecimal("50000")) > 0);
    }
}